# -*-coding:utf-8-*-
"""
从样本中过滤出 时长较长的pkl
从样本中过滤出 时长较短的pkl

"""
import pickle
import os
from pathlib import Path
import multiprocessing as mp
from typing import List, Set, Generator, Tuple
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm

from ReadTypeFile_Speed import rglob_optimized_generator, rglob_optimized
from multiprocessing import Pool
from multiParallel_toolkit import parallel_process, process_parallel
from functools import partial


def deal_a_sample(a_pkl, limit):
    with open(a_pkl, "rb") as fp:
        data = pickle.load(fp)
    time = data["end"] - data["start"]
    max_val, min_val = limit[-1], limit[0]
    if time >= max_val:
        move_file(a_pkl, name_pre=f"LongTime_{max_val}")
        print(a_pkl.name, time)
    elif time <= min_val:
        move_file(a_pkl, name_pre=f"ShortTime_{min_val}")
        print(a_pkl.name, time)
    else:
        skpts = data["ori_skpts"]
        person = 0
        for skpt_lst in skpts:
            if skpt_lst[1] is not None or skpts[-1] is not None:
                person += 1  # 有人，位置未知
        if person < data["len_status"] * 0.35:
            move_file(a_pkl, name_pre="One_Person")
            print(a_pkl.name, f"Pn{person}")
        else:
            print(a_pkl.name, f"rate{person}")


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in os.walk(directory):
        dirnames[:] = [d for d in dirnames if not d.startswith(".")]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    # try:
    with os.scandir(root_directory) as entries:
        top_dirs = [
            entry.path
            for entry in entries
            if entry.is_dir() and not entry.name.startswith(".")
        ]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(
                entry.name.lower().endswith(ext) for ext in extensions
            ):
                root_files.append(os.path.join(root_directory, entry.name))
    # except (PermissionError, OSError):
    #     return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    # try:
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in tqdm(results):
            all_files.extend(result)
    # except Exception as e:
    #     print(f"多进程处理出错，回退到单线程: {e}")
    #     # 回退到单线程处理
    #     for directory in top_dirs:
    #         try:
    #             for dirpath, dirnames, filenames in os.walk(directory):
    #                 dirnames[:] = [d for d in dirnames if not d.startswith('.')]
    #                 for filename in filenames:
    #                     if any(filename.lower().endswith(ext) for ext in extensions):
    #                         all_files.append(os.path.join(dirpath, filename))
    #         except (PermissionError, OSError):
    #             continue

    return all_files


def move_file(a_pkl, name_pre="LongTime"):
    src3 = str(a_pkl.with_suffix(".avi"))
    src2_lst = list((p := a_pkl).parent.glob(f"{p.stem}-*.jpg"))  # 海象运算

    # model-check / 模型认为的标签 / 真实类别
    key_dst = a_pkl.parents[1] / name_pre / a_pkl.stem
    key_dst.mkdir(exist_ok=True, parents=True)
    Path(src3).rename(key_dst / Path(src3).name)
    a_pkl.rename(key_dst / a_pkl.name)  # txt last
    [src2.rename(key_dst / src2.name) for src2 in src2_lst]

    try:
        a_pkl.parent.rmdir()  # 删除空文件夹
    except:
        pass


@parallel_process(process_parallel())
def deal_a_sample_parallel(a_pkl, limit):
    """
    需要重新定义
    """
    return deal_a_sample(a_pkl, limit)


if __name__ == "__main__":
    path = f"/media/pyl/WD_Blue_1T/All_proj/pose_rgb/0_normal/Normal_scls_onePerson"

    # pkls = list(Path(path).rglob('*.pkl'))
    # pkls = find_files_multiprocess(path, ['*.pkl'])  # 类别下的所有样本
    pkls = rglob_optimized(path, f"*.pkl", max_workers=4)

    # deal_a_sample(a_pkl, limit) for a_pkl in pkls
    deal_a_sample_parallel(pkls, limit=(20, 45))
